using System;
using System.Data.SQLite;
using WorkSpaceManager.Data;

namespace WorkSpaceManager.Services
{
    public static class ReportService
    {
        public static decimal GetTodaySales()
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"
                SELECT 
                    COALESCE(SUM(s.TotalAmount), 0) + COALESCE(SUM(p.TotalPrice), 0) as TotalSales
                FROM Sessions s
                LEFT JOIN Purchases p ON s.Id = p.SessionId
                WHERE DATE(s.StartTime) = DATE('now', 'localtime')";
            
            using var command = new SQLiteCommand(query, connection);
            var result = command.ExecuteScalar();
            
            return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
        }

        public static decimal GetWeeklySales(DateTime weekStart)
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var weekEnd = weekStart.AddDays(7);
            
            var query = @"
                SELECT 
                    COALESCE(SUM(s.TotalAmount), 0) + COALESCE(SUM(p.TotalPrice), 0) as TotalSales
                FROM Sessions s
                LEFT JOIN Purchases p ON s.Id = p.SessionId
                WHERE s.StartTime >= @weekStart AND s.StartTime < @weekEnd";
            
            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@weekStart", weekStart);
            command.Parameters.AddWithValue("@weekEnd", weekEnd);
            
            var result = command.ExecuteScalar();
            return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
        }

        public static decimal GetMonthlySales(DateTime month)
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var monthStart = new DateTime(month.Year, month.Month, 1);
            var monthEnd = monthStart.AddMonths(1);
            
            var query = @"
                SELECT 
                    COALESCE(SUM(s.TotalAmount), 0) + COALESCE(SUM(p.TotalPrice), 0) as TotalSales
                FROM Sessions s
                LEFT JOIN Purchases p ON s.Id = p.SessionId
                WHERE s.StartTime >= @monthStart AND s.StartTime < @monthEnd";
            
            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@monthStart", monthStart);
            command.Parameters.AddWithValue("@monthEnd", monthEnd);
            
            var result = command.ExecuteScalar();
            return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
        }

        public static decimal GetMonthlyProfit(DateTime month)
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var monthStart = new DateTime(month.Year, month.Month, 1);
            var monthEnd = monthStart.AddMonths(1);
            
            var query = @"
                SELECT 
                    COALESCE(SUM(s.TotalAmount), 0) + 
                    COALESCE(SUM(p.TotalPrice - (pr.Cost * p.Quantity)), 0) as TotalProfit
                FROM Sessions s
                LEFT JOIN Purchases p ON s.Id = p.SessionId
                LEFT JOIN Products pr ON p.ProductId = pr.Id
                WHERE s.StartTime >= @monthStart AND s.StartTime < @monthEnd";
            
            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@monthStart", monthStart);
            command.Parameters.AddWithValue("@monthEnd", monthEnd);
            
            var result = command.ExecuteScalar();
            return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
        }

        public static decimal GetDailySales(DateTime date)
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"
                SELECT 
                    COALESCE(SUM(s.TotalAmount), 0) + COALESCE(SUM(p.TotalPrice), 0) as TotalSales
                FROM Sessions s
                LEFT JOIN Purchases p ON s.Id = p.SessionId
                WHERE DATE(s.StartTime) = DATE(@date)";
            
            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@date", date.Date);
            
            var result = command.ExecuteScalar();
            return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
        }

        public static decimal GetDailyProfit(DateTime date)
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"
                SELECT 
                    COALESCE(SUM(s.TotalAmount), 0) + 
                    COALESCE(SUM(p.TotalPrice - (pr.Cost * p.Quantity)), 0) as TotalProfit
                FROM Sessions s
                LEFT JOIN Purchases p ON s.Id = p.SessionId
                LEFT JOIN Products pr ON p.ProductId = pr.Id
                WHERE DATE(s.StartTime) = DATE(@date)";
            
            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@date", date.Date);
            
            var result = command.ExecuteScalar();
            return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
        }

        public static int GetTotalCustomersCount()
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT COUNT(*) FROM Customers WHERE IsActive = 1";
            using var command = new SQLiteCommand(query, connection);
            
            return Convert.ToInt32(command.ExecuteScalar());
        }

        public static int GetActiveSessionsCount()
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT COUNT(*) FROM Sessions WHERE IsActive = 1 AND EndTime IS NULL";
            using var command = new SQLiteCommand(query, connection);
            
            return Convert.ToInt32(command.ExecuteScalar());
        }

        public static decimal GetShiftSales(int shiftId)
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"
                SELECT 
                    COALESCE(SUM(s.TotalAmount), 0) + COALESCE(SUM(p.TotalPrice), 0) as TotalSales
                FROM Sessions s
                LEFT JOIN Purchases p ON s.Id = p.SessionId
                WHERE s.ShiftId = @shiftId";
            
            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@shiftId", shiftId);
            
            var result = command.ExecuteScalar();
            return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
        }

        public static decimal GetShiftProfit(int shiftId)
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"
                SELECT 
                    COALESCE(SUM(s.TotalAmount), 0) + 
                    COALESCE(SUM(p.TotalPrice - (pr.Cost * p.Quantity)), 0) as TotalProfit
                FROM Sessions s
                LEFT JOIN Purchases p ON s.Id = p.SessionId
                LEFT JOIN Products pr ON p.ProductId = pr.Id
                WHERE s.ShiftId = @shiftId";
            
            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@shiftId", shiftId);
            
            var result = command.ExecuteScalar();
            return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
        }
    }
}
