<Window x:Class="WorkSpaceManager.Views.CustomerHistoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تاريخ العميل" 
        Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان ومعلومات العميل -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="تاريخ العميل" 
                             FontSize="24" 
                             FontWeight="Bold" 
                             Foreground="#2196F3"
                             Margin="0,0,0,10"/>
                    
                    <TextBlock x:Name="TxtCustomerName" 
                             Text="اسم العميل" 
                             FontSize="18" 
                             FontWeight="Bold"/>
                    
                    <TextBlock x:Name="TxtCustomerPhone" 
                             Text="رقم الهاتف" 
                             FontSize="14" 
                             Foreground="Gray"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Left">
                    <TextBlock Text="إحصائيات العميل" 
                             FontSize="16" 
                             FontWeight="Bold" 
                             Margin="0,0,0,10"/>
                    
                    <TextBlock x:Name="TxtTotalSessions" 
                             Text="إجمالي الجلسات: 0"/>
                    
                    <TextBlock x:Name="TxtTotalHours" 
                             Text="إجمالي الساعات: 0.00"/>
                    
                    <TextBlock x:Name="TxtTotalAmount" 
                             Text="إجمالي المبلغ: 0.00 ريال"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- فلتر التواريخ -->
        <materialDesign:Card Grid.Row="1" Padding="15" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="فترة البحث:" 
                         VerticalAlignment="Center" 
                         FontWeight="Bold" 
                         Margin="0,0,10,0"/>
                
                <DatePicker x:Name="DpFromDate" 
                          Width="150" 
                          Margin="0,0,10,0"
                          SelectedDateChanged="DateFilter_Changed"/>
                
                <TextBlock Text="إلى" 
                         VerticalAlignment="Center" 
                         Margin="0,0,10,0"/>
                
                <DatePicker x:Name="DpToDate" 
                          Width="150" 
                          Margin="0,0,10,0"
                          SelectedDateChanged="DateFilter_Changed"/>
                
                <Button Content="🔍 بحث" 
                        Padding="15,5"
                        Background="#2196F3"
                        Foreground="White"
                        Margin="10,0,0,0"
                        Click="BtnFilter_Click"/>
                
                <Button Content="🔄 إعادة تعيين" 
                        Padding="15,5"
                        Background="#9E9E9E"
                        Foreground="White"
                        Margin="10,0,0,0"
                        Click="BtnReset_Click"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- تاريخ الجلسات -->
        <TabControl Grid.Row="2">
            <TabItem Header="📋 الجلسات">
                <DataGrid x:Name="DgSessions" 
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الرقم" 
                                          Binding="{Binding Id}" 
                                          Width="80"/>
                        
                        <DataGridTextColumn Header="تاريخ البداية" 
                                          Binding="{Binding StartTime, StringFormat='{0:yyyy-MM-dd HH:mm}'}" 
                                          Width="150"/>
                        
                        <DataGridTextColumn Header="تاريخ النهاية" 
                                          Binding="{Binding EndTime, StringFormat='{0:yyyy-MM-dd HH:mm}'}" 
                                          Width="150"/>
                        
                        <DataGridTextColumn Header="المدة (ساعة)" 
                                          Binding="{Binding TotalHours, StringFormat='{0:F2}'}" 
                                          Width="100"/>
                        
                        <DataGridTextColumn Header="سعر الساعة" 
                                          Binding="{Binding HourlyRate, StringFormat='{0:F2}'}" 
                                          Width="100"/>
                        
                        <DataGridTextColumn Header="الإجمالي" 
                                          Binding="{Binding TotalAmount, StringFormat='{0:F2}'}" 
                                          Width="100"/>
                        
                        <DataGridTemplateColumn Header="الحالة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="{Binding IsActive, Converter={StaticResource SessionStatusToColorConverter}}" 
                                            CornerRadius="10" 
                                            Padding="8,4">
                                        <TextBlock Text="{Binding IsActive, Converter={StaticResource SessionStatusToTextConverter}}" 
                                                 Foreground="White" 
                                                 FontWeight="Bold" 
                                                 HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <DataGridTextColumn Header="ملاحظات" 
                                          Binding="{Binding Notes}" 
                                          Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
            
            <TabItem Header="🛍️ المشتريات">
                <DataGrid x:Name="DgPurchases" 
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الجلسة" 
                                          Binding="{Binding SessionId}" 
                                          Width="100"/>
                        
                        <DataGridTextColumn Header="المنتج" 
                                          Binding="{Binding ProductName}" 
                                          Width="200"/>
                        
                        <DataGridTextColumn Header="الكمية" 
                                          Binding="{Binding Quantity}" 
                                          Width="80"/>
                        
                        <DataGridTextColumn Header="سعر الوحدة" 
                                          Binding="{Binding UnitPrice, StringFormat='{0:F2}'}" 
                                          Width="100"/>
                        
                        <DataGridTextColumn Header="الإجمالي" 
                                          Binding="{Binding TotalPrice, StringFormat='{0:F2}'}" 
                                          Width="100"/>
                        
                        <DataGridTextColumn Header="وقت الشراء" 
                                          Binding="{Binding PurchaseTime, StringFormat='{0:yyyy-MM-dd HH:mm}'}" 
                                          Width="150"/>
                        
                        <DataGridTextColumn Header="ملاحظات" 
                                          Binding="{Binding Notes}" 
                                          Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
        </TabControl>

        <!-- الأزرار -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center"
                    Margin="0,20,0,0">
            
            <Button Content="📊 تقرير مفصل" 
                    Padding="15,10"
                    Background="#4CAF50"
                    Foreground="White"
                    Margin="0,0,10,0"
                    Click="BtnDetailedReport_Click"/>
            
            <Button Content="🖨️ طباعة" 
                    Padding="15,10"
                    Background="#2196F3"
                    Foreground="White"
                    Margin="0,0,10,0"
                    Click="BtnPrint_Click"/>
            
            <Button Content="إغلاق" 
                    Padding="15,10"
                    Background="#9E9E9E"
                    Foreground="White"
                    Click="BtnClose_Click"/>
        </StackPanel>
    </Grid>

    <Window.Resources>
        <!-- Converters -->
        <local:SessionStatusToColorConverter x:Key="SessionStatusToColorConverter"/>
        <local:SessionStatusToTextConverter x:Key="SessionStatusToTextConverter"/>
    </Window.Resources>
</Window>
