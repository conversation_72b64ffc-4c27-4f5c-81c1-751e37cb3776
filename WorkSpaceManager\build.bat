@echo off
echo ===============================================
echo    Work Space Manager - Build Script
echo ===============================================
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK is not installed or not in PATH
    echo Please install .NET 6.0 SDK from: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET SDK found. Building project...
echo.

REM Clean previous builds
echo Cleaning previous builds...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"

REM Restore packages
echo Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)

REM Build the project
echo Building the project...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

REM Publish the project
echo Publishing the project...
dotnet publish --configuration Release --output "publish" --self-contained true --runtime win-x64
if %errorlevel% neq 0 (
    echo ERROR: Publish failed
    pause
    exit /b 1
)

REM Create Data directory in publish folder
echo Creating Data directory...
if not exist "publish\Data" mkdir "publish\Data"
if not exist "publish\Data\Backups" mkdir "publish\Data\Backups"
if not exist "publish\Data\Invoices" mkdir "publish\Data\Invoices"

REM Copy additional files
echo Copying additional files...
copy "README.md" "publish\" >nul 2>&1

echo.
echo ===============================================
echo Build completed successfully!
echo.
echo Published files are in the 'publish' folder
echo You can now create a setup installer from these files
echo ===============================================
echo.
pause
