@echo off
echo ===============================================
echo    إصلاح مراجع SQLite في المشروع
echo ===============================================
echo.

echo جاري إصلاح ملفات الخدمات...

REM Fix all SQLite references using PowerShell
powershell -ExecutionPolicy Bypass -File "fix-sqlite-references.ps1"

echo.
echo تنظيف المشروع...
dotnet clean

echo.
echo استعادة الحزم...
dotnet restore

echo.
echo بناء المشروع...
dotnet build --configuration Release

if %errorlevel% equ 0 (
    echo.
    echo ===============================================
    echo تم إصلاح المشروع بنجاح!
    echo يمكنك الآن تشغيل البرنامج باستخدام run.bat
    echo ===============================================
) else (
    echo.
    echo ===============================================
    echo فشل في بناء المشروع
    echo يرجى التحقق من الأخطاء أعلاه
    echo ===============================================
)

echo.
pause
