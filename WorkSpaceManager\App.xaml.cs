using System;
using System.IO;
using System.Windows;
using WorkSpaceManager.Data;
using WorkSpaceManager.Services;

namespace WorkSpaceManager
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // إنشاء مجلد البيانات إذا لم يكن موجوداً
            var dataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            if (!Directory.Exists(dataPath))
            {
                Directory.CreateDirectory(dataPath);
            }
            
            // تهيئة قاعدة البيانات
            try
            {
                DatabaseService.InitializeDatabase();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
                return;
            }
            
            // بدء خدمة النسخ الاحتياطي التلقائي
            BackupService.StartAutoBackup();
        }
        
        protected override void OnExit(ExitEventArgs e)
        {
            // إيقاف خدمة النسخ الاحتياطي
            BackupService.StopAutoBackup();
            base.OnExit(e);
        }
    }
}
