<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Basic Card Style -->
    <Style x:Key="BasicCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
        <Setter Property="Background" Value="White"/>
    </Style>

    <!-- Statistic Card Style -->
    <Style x:Key="StatisticCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource BasicCardStyle}">
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
    </Style>

    <!-- Primary Card Style -->
    <Style x:Key="PrimaryCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource BasicCardStyle}">
        <Setter Property="Background" Value="#2196F3"/>
    </Style>

    <!-- Success Card Style -->
    <Style x:Key="SuccessCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource BasicCardStyle}">
        <Setter Property="Background" Value="#4CAF50"/>
    </Style>

    <!-- Warning Card Style -->
    <Style x:Key="WarningCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource BasicCardStyle}">
        <Setter Property="Background" Value="#FF9800"/>
    </Style>

    <!-- Error Card Style -->
    <Style x:Key="ErrorCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource BasicCardStyle}">
        <Setter Property="Background" Value="#F44336"/>
    </Style>

    <!-- Info Card Style -->
    <Style x:Key="InfoCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource BasicCardStyle}">
        <Setter Property="Background" Value="#E3F2FD"/>
        <Setter Property="BorderBrush" Value="#2196F3"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- Elevated Card Style -->
    <Style x:Key="ElevatedCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource BasicCardStyle}">
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth4"/>
        <Setter Property="Margin" Value="15"/>
    </Style>

    <!-- Compact Card Style -->
    <Style x:Key="CompactCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource BasicCardStyle}">
        <Setter Property="Padding" Value="10"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
    </Style>

</ResourceDictionary>
