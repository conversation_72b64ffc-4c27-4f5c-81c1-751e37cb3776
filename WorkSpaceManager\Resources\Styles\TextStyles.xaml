<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Page Title Style -->
    <Style x:Key="PageTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="28"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="#2196F3"/>
        <Setter Property="Margin" Value="0,0,0,20"/>
    </Style>

    <!-- Section Header Style -->
    <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="#424242"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
    </Style>

    <!-- Label Style -->
    <Style x:Key="LabelStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="#616161"/>
        <Setter Property="Margin" Value="0,0,0,5"/>
    </Style>

    <!-- Value Style -->
    <Style x:Key="ValueStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="#212121"/>
    </Style>

    <!-- Highlight Value Style -->
    <Style x:Key="HighlightValueStyle" TargetType="TextBlock" BasedOn="{StaticResource ValueStyle}">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="#2196F3"/>
    </Style>

    <!-- Success Value Style -->
    <Style x:Key="SuccessValueStyle" TargetType="TextBlock" BasedOn="{StaticResource ValueStyle}">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="#4CAF50"/>
    </Style>

    <!-- Error Value Style -->
    <Style x:Key="ErrorValueStyle" TargetType="TextBlock" BasedOn="{StaticResource ValueStyle}">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="#F44336"/>
    </Style>

    <!-- Warning Value Style -->
    <Style x:Key="WarningValueStyle" TargetType="TextBlock" BasedOn="{StaticResource ValueStyle}">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="#FF9800"/>
    </Style>

    <!-- Caption Style -->
    <Style x:Key="CaptionStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="#757575"/>
        <Setter Property="FontStyle" Value="Italic"/>
    </Style>

    <!-- Card Title Style -->
    <Style x:Key="CardTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="#212121"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
    </Style>

    <!-- Statistic Number Style -->
    <Style x:Key="StatisticNumberStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="36"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- Statistic Label Style -->
    <Style x:Key="StatisticLabelStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

</ResourceDictionary>
