<Page x:Class="WorkSpaceManager.Views.ProductsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="إدارة المنتجات والمشروبات"
      FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="إدارة المنتجات والمشروبات" 
                   FontSize="28" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#2196F3"/>

        <!-- شريط الأدوات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- البحث والفلتر -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBox x:Name="TxtSearch" 
                         Width="250" 
                         Height="35"
                         VerticalContentAlignment="Center"
                         materialDesign:HintAssist.Hint="البحث في المنتجات..."
                         TextChanged="TxtSearch_TextChanged"/>
                
                <ComboBox x:Name="CmbCategoryFilter" 
                          Width="150" 
                          Height="35"
                          Margin="10,0,0,0"
                          materialDesign:HintAssist.Hint="فلتر حسب الفئة"
                          SelectionChanged="CmbCategoryFilter_SelectionChanged"/>
                
                <Button Content="🔍 بحث" 
                        Margin="10,0,0,0"
                        Padding="15,5"
                        Background="#2196F3"
                        Foreground="White"
                        Click="BtnSearch_Click"/>
                
                <Button Content="🔄 تحديث" 
                        Margin="10,0,0,0"
                        Padding="15,5"
                        Background="#4CAF50"
                        Foreground="White"
                        Click="BtnRefresh_Click"/>
            </StackPanel>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button x:Name="BtnAddProduct" 
                        Content="➕ إضافة منتج جديد" 
                        Padding="15,10"
                        Background="#4CAF50"
                        Foreground="White"
                        Click="BtnAddProduct_Click"/>
                
                <Button x:Name="BtnEditProduct" 
                        Content="✏️ تعديل" 
                        Margin="10,0,0,0"
                        Padding="15,10"
                        Background="#FF9800"
                        Foreground="White"
                        IsEnabled="False"
                        Click="BtnEditProduct_Click"/>
                
                <Button x:Name="BtnDeleteProduct" 
                        Content="🗑️ حذف" 
                        Margin="10,0,0,0"
                        Padding="15,10"
                        Background="#F44336"
                        Foreground="White"
                        IsEnabled="False"
                        Click="BtnDeleteProduct_Click"/>
            </StackPanel>
        </Grid>

        <!-- قائمة المنتجات -->
        <materialDesign:Card Grid.Row="2" Padding="0">
            <DataGrid x:Name="DgProducts" 
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      SelectionChanged="DgProducts_SelectionChanged"
                      MouseDoubleClick="DgProducts_MouseDoubleClick">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم" 
                                      Binding="{Binding Id}" 
                                      Width="80"/>
                    
                    <DataGridTextColumn Header="اسم المنتج" 
                                      Binding="{Binding Name}" 
                                      Width="200"/>
                    
                    <DataGridTextColumn Header="الفئة" 
                                      Binding="{Binding Category}" 
                                      Width="120"/>
                    
                    <DataGridTextColumn Header="السعر" 
                                      Binding="{Binding Price, StringFormat='{0:F2}'}" 
                                      Width="100"/>
                    
                    <DataGridTextColumn Header="التكلفة" 
                                      Binding="{Binding Cost, StringFormat='{0:F2}'}" 
                                      Width="100"/>
                    
                    <DataGridTemplateColumn Header="الربح" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Profit, StringFormat='{0:F2}'}" 
                                         Foreground="#4CAF50" 
                                         FontWeight="Bold"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <DataGridTextColumn Header="الكمية" 
                                      Binding="{Binding Quantity}" 
                                      Width="80"/>
                    
                    <DataGridTextColumn Header="الباركود" 
                                      Binding="{Binding Barcode}" 
                                      Width="120"/>
                    
                    <DataGridTemplateColumn Header="الحالة" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Background="{Binding IsActive, Converter={StaticResource BoolToColorConverter}}" 
                                        CornerRadius="10" 
                                        Padding="8,4">
                                    <TextBlock Text="{Binding IsActive, Converter={StaticResource BoolToStatusConverter}}" 
                                             Foreground="White" 
                                             FontWeight="Bold" 
                                             HorizontalAlignment="Center"/>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <DataGridTemplateColumn Header="الإجراءات" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="📦" 
                                            ToolTip="تحديث الكمية"
                                            Margin="2"
                                            Padding="8,4"
                                            Background="#FF9800"
                                            Foreground="White"
                                            Click="BtnUpdateQuantity_Click"
                                            Tag="{Binding Id}"/>
                                    
                                    <Button Content="🛒" 
                                            ToolTip="إضافة للجلسة"
                                            Margin="2"
                                            Padding="8,4"
                                            Background="#4CAF50"
                                            Foreground="White"
                                            Click="BtnAddToSession_Click"
                                            Tag="{Binding Id}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>

                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Height" Value="50"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="#BBDEFB"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </materialDesign:Card>
    </Grid>

    <Page.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        
        <!-- Custom Converters -->
        <local:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <local:BoolToStatusConverter x:Key="BoolToStatusConverter"/>
    </Page.Resources>
</Page>
