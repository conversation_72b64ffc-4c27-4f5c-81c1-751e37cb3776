using System;
using System.Windows;
using System.Windows.Controls;
using MaterialDesignThemes.Wpf;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class MainWindow : Window
    {
        private bool _isDarkTheme = false;

        public MainWindow()
        {
            InitializeComponent();
            LoadCurrentShiftInfo();
            
            // تحديث معلومات الشيفت كل دقيقة
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromMinutes(1);
            timer.Tick += (s, e) => LoadCurrentShiftInfo();
            timer.Start();
        }

        private void LoadCurrentShiftInfo()
        {
            try
            {
                var currentShift = ShiftService.GetCurrentShift();
                if (currentShift != null)
                {
                    TxtCurrentShift.Text = $"{currentShift.Name} - {currentShift.EmployeeName}";
                    var duration = DateTime.Now - currentShift.StartTime;
                    TxtShiftTime.Text = $"المدة: {duration.Hours:00}:{duration.Minutes:00}";
                }
                else
                {
                    TxtCurrentShift.Text = "لا يوجد شيفت نشط";
                    TxtShiftTime.Text = "";
                }
            }
            catch
            {
                TxtCurrentShift.Text = "خطأ في تحميل بيانات الشيفت";
                TxtShiftTime.Text = "";
            }
        }

        private void BtnDashboard_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new Uri("Views/DashboardPage.xaml", UriKind.Relative));
            HighlightSelectedButton(sender as Button);
        }

        private void BtnCustomers_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new Uri("Views/CustomersPage.xaml", UriKind.Relative));
            HighlightSelectedButton(sender as Button);
        }

        private void BtnSessions_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new Uri("Views/SessionsPage.xaml", UriKind.Relative));
            HighlightSelectedButton(sender as Button);
        }

        private void BtnProducts_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new Uri("Views/ProductsPage.xaml", UriKind.Relative));
            HighlightSelectedButton(sender as Button);
        }

        private void BtnInvoices_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new Uri("Views/InvoicesPage.xaml", UriKind.Relative));
            HighlightSelectedButton(sender as Button);
        }

        private void BtnShifts_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new Uri("Views/ShiftsPage.xaml", UriKind.Relative));
            HighlightSelectedButton(sender as Button);
        }

        private void BtnReports_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new Uri("Views/ReportsPage.xaml", UriKind.Relative));
            HighlightSelectedButton(sender as Button);
        }

        private void BtnSettings_Click(object sender, RoutedEventArgs e)
        {
            MainFrame.Navigate(new Uri("Views/SettingsPage.xaml", UriKind.Relative));
            HighlightSelectedButton(sender as Button);
        }

        private void BtnThemeToggle_Click(object sender, RoutedEventArgs e)
        {
            _isDarkTheme = !_isDarkTheme;
            
            var paletteHelper = new PaletteHelper();
            var theme = paletteHelper.GetTheme();
            
            if (_isDarkTheme)
            {
                theme.SetBaseTheme(Theme.Dark);
                BtnThemeToggle.Content = "☀️ الوضع النهاري";
            }
            else
            {
                theme.SetBaseTheme(Theme.Light);
                BtnThemeToggle.Content = "🌙 الوضع الليلي";
            }
            
            paletteHelper.SetTheme(theme);
        }

        private void HighlightSelectedButton(Button selectedButton)
        {
            // إزالة التمييز من جميع الأزرار
            BtnDashboard.Background = System.Windows.Media.Brushes.Transparent;
            BtnCustomers.Background = System.Windows.Media.Brushes.Transparent;
            BtnSessions.Background = System.Windows.Media.Brushes.Transparent;
            BtnProducts.Background = System.Windows.Media.Brushes.Transparent;
            BtnInvoices.Background = System.Windows.Media.Brushes.Transparent;
            BtnShifts.Background = System.Windows.Media.Brushes.Transparent;
            BtnReports.Background = System.Windows.Media.Brushes.Transparent;
            BtnSettings.Background = System.Windows.Media.Brushes.Transparent;

            // تمييز الزر المحدد
            if (selectedButton != null)
            {
                selectedButton.Background = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(63, 81, 181));
            }
        }
    }
}
