<Application x:Class="WorkSpaceManager.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary Source="Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="Resources/Styles/TextStyles.xaml"/>
                <ResourceDictionary Source="Resources/Styles/CardStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Global Colors -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#FFC107"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            
            <!-- Dark Theme Colors -->
            <SolidColorBrush x:Key="DarkBackgroundBrush" Color="#121212"/>
            <SolidColorBrush x:Key="DarkSurfaceBrush" Color="#1E1E1E"/>
            <SolidColorBrush x:Key="DarkTextBrush" Color="#FFFFFF"/>
            
        </ResourceDictionary>
    </Application.Resources>
</Application>
