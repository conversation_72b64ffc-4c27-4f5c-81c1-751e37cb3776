using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using WorkSpaceManager.Models;

namespace WorkSpaceManager.Services
{
    public static class PrintService
    {
        private static Session _currentSession;
        private static Font _titleFont = new Font("Arial", 16, FontStyle.Bold);
        private static Font _headerFont = new Font("Arial", 12, FontStyle.Bold);
        private static Font _normalFont = new Font("Arial", 10);
        private static Font _smallFont = new Font("Arial", 8);

        public static void PrintInvoice(Session session)
        {
            _currentSession = session;
            
            var printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;
            
            // إعدادات الطباعة للطابعات الحرارية
            printDocument.DefaultPageSettings.PaperSize = new PaperSize("Receipt", 315, 600); // 80mm width
            printDocument.DefaultPageSettings.Margins = new Margins(10, 10, 10, 10);
            
            try
            {
                printDocument.Print();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الطباعة: {ex.Message}");
            }
        }

        private static void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            var graphics = e.Graphics;
            var yPosition = 20;
            var leftMargin = 10;
            var rightMargin = e.PageBounds.Width - 10;
            var centerX = e.PageBounds.Width / 2;

            // عنوان الفاتورة
            var title = "Work Space Manager";
            var titleSize = graphics.MeasureString(title, _titleFont);
            graphics.DrawString(title, _titleFont, Brushes.Black, centerX - titleSize.Width / 2, yPosition);
            yPosition += (int)titleSize.Height + 5;

            var subtitle = "فاتورة جلسة عمل";
            var subtitleSize = graphics.MeasureString(subtitle, _headerFont);
            graphics.DrawString(subtitle, _headerFont, Brushes.Black, centerX - subtitleSize.Width / 2, yPosition);
            yPosition += (int)subtitleSize.Height + 15;

            // خط فاصل
            graphics.DrawLine(Pens.Black, leftMargin, yPosition, rightMargin, yPosition);
            yPosition += 10;

            // معلومات الفاتورة
            var invoiceDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
            graphics.DrawString($"تاريخ الفاتورة: {invoiceDate}", _normalFont, Brushes.Black, leftMargin, yPosition);
            yPosition += 20;

            graphics.DrawString($"رقم الفاتورة: INV-{_currentSession.Id:000000}", _normalFont, Brushes.Black, leftMargin, yPosition);
            yPosition += 25;

            // معلومات العميل
            var customer = CustomerService.GetCustomerById(_currentSession.CustomerId);
            if (customer != null)
            {
                graphics.DrawString("معلومات العميل:", _headerFont, Brushes.Black, leftMargin, yPosition);
                yPosition += 20;

                graphics.DrawString($"الاسم: {customer.Name}", _normalFont, Brushes.Black, leftMargin, yPosition);
                yPosition += 15;

                if (!string.IsNullOrEmpty(customer.Phone))
                {
                    graphics.DrawString($"الهاتف: {customer.Phone}", _normalFont, Brushes.Black, leftMargin, yPosition);
                    yPosition += 15;
                }
            }

            yPosition += 10;

            // معلومات الجلسة
            graphics.DrawString("تفاصيل الجلسة:", _headerFont, Brushes.Black, leftMargin, yPosition);
            yPosition += 20;

            graphics.DrawString($"وقت البداية: {_currentSession.StartTime:yyyy-MM-dd HH:mm}", _normalFont, Brushes.Black, leftMargin, yPosition);
            yPosition += 15;

            if (_currentSession.EndTime.HasValue)
            {
                graphics.DrawString($"وقت النهاية: {_currentSession.EndTime:yyyy-MM-dd HH:mm}", _normalFont, Brushes.Black, leftMargin, yPosition);
                yPosition += 15;
            }

            graphics.DrawString($"المدة: {_currentSession.TotalHours:F2} ساعة", _normalFont, Brushes.Black, leftMargin, yPosition);
            yPosition += 15;

            graphics.DrawString($"سعر الساعة: {_currentSession.HourlyRate:F2} ريال", _normalFont, Brushes.Black, leftMargin, yPosition);
            yPosition += 15;

            graphics.DrawString($"إجمالي الوقت: {_currentSession.TotalAmount:F2} ريال", _headerFont, Brushes.Black, leftMargin, yPosition);
            yPosition += 25;

            // المشتريات
            var purchases = PurchaseService.GetPurchasesBySession(_currentSession.Id);
            if (purchases.Any())
            {
                graphics.DrawString("المشتريات:", _headerFont, Brushes.Black, leftMargin, yPosition);
                yPosition += 20;

                // رؤوس الأعمدة
                graphics.DrawString("المنتج", _normalFont, Brushes.Black, leftMargin, yPosition);
                graphics.DrawString("الكمية", _normalFont, Brushes.Black, leftMargin + 120, yPosition);
                graphics.DrawString("السعر", _normalFont, Brushes.Black, leftMargin + 170, yPosition);
                graphics.DrawString("الإجمالي", _normalFont, Brushes.Black, leftMargin + 220, yPosition);
                yPosition += 15;

                // خط تحت الرؤوس
                graphics.DrawLine(Pens.Black, leftMargin, yPosition, rightMargin, yPosition);
                yPosition += 5;

                decimal purchasesTotal = 0;
                foreach (var purchase in purchases)
                {
                    graphics.DrawString(purchase.ProductName, _smallFont, Brushes.Black, leftMargin, yPosition);
                    graphics.DrawString(purchase.Quantity.ToString(), _smallFont, Brushes.Black, leftMargin + 120, yPosition);
                    graphics.DrawString($"{purchase.UnitPrice:F2}", _smallFont, Brushes.Black, leftMargin + 170, yPosition);
                    graphics.DrawString($"{purchase.TotalPrice:F2}", _smallFont, Brushes.Black, leftMargin + 220, yPosition);
                    
                    purchasesTotal += purchase.TotalPrice;
                    yPosition += 15;
                }

                yPosition += 5;
                graphics.DrawLine(Pens.Black, leftMargin, yPosition, rightMargin, yPosition);
                yPosition += 10;

                graphics.DrawString($"إجمالي المشتريات: {purchasesTotal:F2} ريال", _headerFont, Brushes.Black, leftMargin, yPosition);
                yPosition += 25;
            }

            // الإجمالي النهائي
            var grandTotal = _currentSession.TotalAmount + purchases.Sum(p => p.TotalPrice);
            
            // خط فاصل
            graphics.DrawLine(Pens.Black, leftMargin, yPosition, rightMargin, yPosition);
            yPosition += 10;

            graphics.DrawString($"الإجمالي النهائي: {grandTotal:F2} ريال", _titleFont, Brushes.Black, leftMargin, yPosition);
            yPosition += 30;

            // رسالة شكر
            var thankYou = "شكراً لزيارتكم";
            var thankYouSize = graphics.MeasureString(thankYou, _headerFont);
            graphics.DrawString(thankYou, _headerFont, Brushes.Black, centerX - thankYouSize.Width / 2, yPosition);
            yPosition += 20;

            var visitAgain = "نتطلع لزيارتكم مرة أخرى";
            var visitAgainSize = graphics.MeasureString(visitAgain, _normalFont);
            graphics.DrawString(visitAgain, _normalFont, Brushes.Black, centerX - visitAgainSize.Width / 2, yPosition);
        }

        public static void PrintDailyReport(DateTime date)
        {
            // يمكن إضافة طباعة التقرير اليومي هنا
            throw new NotImplementedException("طباعة التقرير اليومي ستتم إضافتها لاحقاً");
        }

        public static void PrintShiftReport(int shiftId)
        {
            // يمكن إضافة طباعة تقرير الشيفت هنا
            throw new NotImplementedException("طباعة تقرير الشيفت ستتم إضافتها لاحقاً");
        }
    }
}
