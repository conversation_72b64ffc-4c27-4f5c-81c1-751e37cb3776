<Window x:Class="WorkSpaceManager.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Work Space Manager - نظام إدارة المساحات المشتركة"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <Window.Resources>
        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#3F51B5"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#303F9F"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- القائمة الجانبية -->
        <Border Grid.Column="0" Background="#2196F3">
            <StackPanel>
                <!-- شعار البرنامج -->
                <Border Background="#1976D2" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Work Space Manager" 
                                 FontSize="18" 
                                 FontWeight="Bold" 
                                 Foreground="White" 
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="نظام إدارة المساحات المشتركة" 
                                 FontSize="12" 
                                 Foreground="White" 
                                 HorizontalAlignment="Center" 
                                 Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- أزرار القائمة -->
                <Button x:Name="BtnDashboard" 
                        Content="🏠 الرئيسية" 
                        Style="{StaticResource MenuButtonStyle}"
                        Click="BtnDashboard_Click"/>
                
                <Button x:Name="BtnCustomers" 
                        Content="👥 إدارة العملاء" 
                        Style="{StaticResource MenuButtonStyle}"
                        Click="BtnCustomers_Click"/>
                
                <Button x:Name="BtnSessions" 
                        Content="⏰ الجلسات النشطة" 
                        Style="{StaticResource MenuButtonStyle}"
                        Click="BtnSessions_Click"/>
                
                <Button x:Name="BtnProducts" 
                        Content="🛍️ المنتجات والمشروبات" 
                        Style="{StaticResource MenuButtonStyle}"
                        Click="BtnProducts_Click"/>
                
                <Button x:Name="BtnInvoices" 
                        Content="🧾 الفواتير" 
                        Style="{StaticResource MenuButtonStyle}"
                        Click="BtnInvoices_Click"/>
                
                <Button x:Name="BtnShifts" 
                        Content="🔄 إدارة الشيفتات" 
                        Style="{StaticResource MenuButtonStyle}"
                        Click="BtnShifts_Click"/>
                
                <Button x:Name="BtnReports" 
                        Content="📊 التقارير" 
                        Style="{StaticResource MenuButtonStyle}"
                        Click="BtnReports_Click"/>
                
                <Button x:Name="BtnSettings" 
                        Content="⚙️ الإعدادات" 
                        Style="{StaticResource MenuButtonStyle}"
                        Click="BtnSettings_Click"/>

                <!-- معلومات الشيفت الحالي -->
                <Border Background="#1976D2" 
                        Margin="10" 
                        CornerRadius="5" 
                        Padding="15">
                    <StackPanel>
                        <TextBlock Text="الشيفت الحالي" 
                                 FontWeight="Bold" 
                                 Foreground="White" 
                                 FontSize="14"/>
                        <TextBlock x:Name="TxtCurrentShift" 
                                 Text="لا يوجد شيفت نشط" 
                                 Foreground="White" 
                                 FontSize="12" 
                                 Margin="0,5,0,0"/>
                        <TextBlock x:Name="TxtShiftTime" 
                                 Text="" 
                                 Foreground="White" 
                                 FontSize="10" 
                                 Margin="0,2,0,0"/>
                    </StackPanel>
                </Border>

                <!-- زر الوضع الليلي -->
                <Button x:Name="BtnThemeToggle" 
                        Content="🌙 الوضع الليلي" 
                        Style="{StaticResource MenuButtonStyle}"
                        Click="BtnThemeToggle_Click"
                        Margin="10"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Border Grid.Column="1" Background="#F5F5F5">
            <Frame x:Name="MainFrame" 
                   NavigationUIVisibility="Hidden"
                   Source="DashboardPage.xaml"/>
        </Border>
    </Grid>
</Window>
