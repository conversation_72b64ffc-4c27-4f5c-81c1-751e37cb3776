@echo off
echo ===============================================
echo    Work Space Manager - تشغيل البرنامج
echo ===============================================
echo.

REM Check if .NET is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: .NET SDK غير مثبت أو غير موجود في PATH
    echo يمكنك تحميله من: https://dotnet.microsoft.com/download
    echo.
    echo محاولة تشغيل البرنامج من الملفات المبنية...
    
    REM Try to run from publish folder
    if exist "publish\WorkSpaceManager.exe" (
        echo تم العثور على الملف التنفيذي، جاري التشغيل...
        cd publish
        start WorkSpaceManager.exe
        echo تم تشغيل البرنامج بنجاح!
        goto :end
    ) else (
        echo لم يتم العثور على الملف التنفيذي
        echo يرجى بناء المشروع أولاً باستخدام build.bat
        goto :error
    )
)

echo تم العثور على .NET SDK
echo.

REM Check if project is built
if not exist "bin\Release" (
    echo المشروع غير مبني، جاري البناء...
    dotnet build --configuration Release
    if %errorlevel% neq 0 (
        echo فشل في بناء المشروع
        goto :error
    )
)

echo جاري تشغيل البرنامج...
dotnet run --configuration Release

goto :end

:error
echo.
echo ===============================================
echo حدث خطأ في تشغيل البرنامج
echo.
echo الحلول المقترحة:
echo 1. تثبيت .NET 6.0 SDK
echo 2. تشغيل build.bat لبناء المشروع
echo 3. التأكد من وجود جميع الملفات
echo ===============================================
pause
exit /b 1

:end
echo.
echo ===============================================
echo شكراً لاستخدام Work Space Manager
echo ===============================================
pause
