using System;
using System.Collections.Generic;
using System.Data.SQLite;
using WorkSpaceManager.Data;
using WorkSpaceManager.Models;

namespace WorkSpaceManager.Services
{
    public static class ShiftService
    {
        public static Shift GetCurrentShift()
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT * FROM Shifts WHERE IsActive = 1 AND EndTime IS NULL ORDER BY StartTime DESC LIMIT 1";
            using var command = new SQLiteCommand(query, connection);
            using var reader = command.ExecuteReader();
            
            if (reader.Read())
            {
                return new Shift
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    StartTime = reader.GetDateTime("StartTime"),
                    EndTime = reader.IsDBNull("EndTime") ? null : reader.GetDateTime("EndTime"),
                    EmployeeName = reader.IsDBNull("EmployeeName") ? "" : reader.GetString("EmployeeName"),
                    TotalSales = reader.GetDecimal("TotalSales"),
                    TotalCash = reader.GetDecimal("TotalCash"),
                    TotalExpenses = reader.GetDecimal("TotalExpenses"),
                    NetProfit = reader.GetDecimal("NetProfit"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes")
                };
            }
            
            return null;
        }

        public static int StartShift(string name, string employeeName, string notes = "")
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"INSERT INTO Shifts (Name, StartTime, EmployeeName, IsActive, Notes) 
                         VALUES (@name, @startTime, @employeeName, 1, @notes);
                         SELECT last_insert_rowid();";
            
            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@name", name);
            command.Parameters.AddWithValue("@startTime", DateTime.Now);
            command.Parameters.AddWithValue("@employeeName", employeeName);
            command.Parameters.AddWithValue("@notes", notes);
            
            return Convert.ToInt32(command.ExecuteScalar());
        }

        public static bool EndShift(int shiftId, decimal totalCash, decimal totalExpenses, string notes = "")
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            // حساب إجمالي المبيعات من الجلسات والمشتريات
            var salesQuery = @"
                SELECT 
                    COALESCE(SUM(s.TotalAmount), 0) + COALESCE(SUM(p.TotalPrice), 0) as TotalSales
                FROM Sessions s
                LEFT JOIN Purchases p ON s.Id = p.SessionId
                WHERE s.ShiftId = @shiftId";
            
            using var salesCommand = new SQLiteCommand(salesQuery, connection);
            salesCommand.Parameters.AddWithValue("@shiftId", shiftId);
            var totalSales = Convert.ToDecimal(salesCommand.ExecuteScalar());
            
            var netProfit = totalSales - totalExpenses;
            
            var updateQuery = @"UPDATE Shifts SET 
                               EndTime = @endTime, 
                               TotalSales = @totalSales, 
                               TotalCash = @totalCash, 
                               TotalExpenses = @totalExpenses, 
                               NetProfit = @netProfit,
                               Notes = @notes
                               WHERE Id = @id";
            
            using var updateCommand = new SQLiteCommand(updateQuery, connection);
            updateCommand.Parameters.AddWithValue("@id", shiftId);
            updateCommand.Parameters.AddWithValue("@endTime", DateTime.Now);
            updateCommand.Parameters.AddWithValue("@totalSales", totalSales);
            updateCommand.Parameters.AddWithValue("@totalCash", totalCash);
            updateCommand.Parameters.AddWithValue("@totalExpenses", totalExpenses);
            updateCommand.Parameters.AddWithValue("@netProfit", netProfit);
            updateCommand.Parameters.AddWithValue("@notes", notes);
            
            return updateCommand.ExecuteNonQuery() > 0;
        }

        public static List<Shift> GetShiftsByDate(DateTime date)
        {
            var shifts = new List<Shift>();
            
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"SELECT * FROM Shifts 
                         WHERE DATE(StartTime) = DATE(@date) 
                         ORDER BY StartTime";
            
            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@date", date.Date);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                shifts.Add(new Shift
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    StartTime = reader.GetDateTime("StartTime"),
                    EndTime = reader.IsDBNull("EndTime") ? null : reader.GetDateTime("EndTime"),
                    EmployeeName = reader.IsDBNull("EmployeeName") ? "" : reader.GetString("EmployeeName"),
                    TotalSales = reader.GetDecimal("TotalSales"),
                    TotalCash = reader.GetDecimal("TotalCash"),
                    TotalExpenses = reader.GetDecimal("TotalExpenses"),
                    NetProfit = reader.GetDecimal("NetProfit"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes")
                });
            }
            
            return shifts;
        }

        public static List<Shift> GetAllShifts()
        {
            var shifts = new List<Shift>();
            
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT * FROM Shifts ORDER BY StartTime DESC";
            using var command = new SQLiteCommand(query, connection);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                shifts.Add(new Shift
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    StartTime = reader.GetDateTime("StartTime"),
                    EndTime = reader.IsDBNull("EndTime") ? null : reader.GetDateTime("EndTime"),
                    EmployeeName = reader.IsDBNull("EmployeeName") ? "" : reader.GetString("EmployeeName"),
                    TotalSales = reader.GetDecimal("TotalSales"),
                    TotalCash = reader.GetDecimal("TotalCash"),
                    TotalExpenses = reader.GetDecimal("TotalExpenses"),
                    NetProfit = reader.GetDecimal("NetProfit"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes")
                });
            }
            
            return shifts;
        }

        public static Shift GetShiftById(int id)
        {
            using var connection = new SQLiteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT * FROM Shifts WHERE Id = @id";
            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            using var reader = command.ExecuteReader();
            
            if (reader.Read())
            {
                return new Shift
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    StartTime = reader.GetDateTime("StartTime"),
                    EndTime = reader.IsDBNull("EndTime") ? null : reader.GetDateTime("EndTime"),
                    EmployeeName = reader.IsDBNull("EmployeeName") ? "" : reader.GetString("EmployeeName"),
                    TotalSales = reader.GetDecimal("TotalSales"),
                    TotalCash = reader.GetDecimal("TotalCash"),
                    TotalExpenses = reader.GetDecimal("TotalExpenses"),
                    NetProfit = reader.GetDecimal("NetProfit"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes")
                };
            }
            
            return null;
        }
    }
}
