<Window x:Class="WorkSpaceManager.Views.CreateInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إنشاء فاتورة" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="إنشاء فاتورة" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#2196F3"
                   HorizontalAlignment="Center"/>

        <!-- اختيار الجلسة -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <StackPanel Grid.Column="0">
                <TextBlock Text="اختيار الجلسة:" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="CmbSessions" 
                          Height="35"
                          SelectionChanged="CmbSessions_SelectionChanged">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <StackPanel>
                                <TextBlock Text="{Binding CustomerName}" FontWeight="Bold"/>
                                <TextBlock Text="{Binding StartTime, StringFormat='بدأ في: {0:yyyy-MM-dd HH:mm}'}" 
                                         FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>
            </StackPanel>
            
            <Button Grid.Column="1" 
                    Content="تحديث" 
                    Margin="10,25,0,0"
                    Padding="15,5"
                    Click="BtnRefresh_Click"/>
        </Grid>

        <!-- تفاصيل الفاتورة -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <StackPanel x:Name="InvoiceDetails" Margin="0,0,0,20">
                
                <!-- معلومات العميل والجلسة -->
                <Border Background="#F5F5F5" 
                        CornerRadius="5" 
                        Padding="15" 
                        Margin="0,0,0,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="معلومات العميل" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                            <TextBlock x:Name="TxtCustomerName" Text="اسم العميل: -"/>
                            <TextBlock x:Name="TxtCustomerPhone" Text="الهاتف: -"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="معلومات الجلسة" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                            <TextBlock x:Name="TxtSessionStart" Text="وقت البداية: -"/>
                            <TextBlock x:Name="TxtSessionEnd" Text="وقت النهاية: -"/>
                            <TextBlock x:Name="TxtSessionDuration" Text="المدة: -"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- تفاصيل الوقت -->
                <Border Background="#E3F2FD" 
                        CornerRadius="5" 
                        Padding="15" 
                        Margin="0,0,0,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="سعر الساعة" FontWeight="Bold"/>
                            <TextBlock x:Name="TxtHourlyRate" Text="0.00 ريال" FontSize="18"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="إجمالي الساعات" FontWeight="Bold"/>
                            <TextBlock x:Name="TxtTotalHours" Text="0.00 ساعة" FontSize="18"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="إجمالي الوقت" FontWeight="Bold"/>
                            <TextBlock x:Name="TxtTimeTotal" Text="0.00 ريال" FontSize="18" Foreground="#2196F3"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- المشتريات -->
                <Border Background="#E8F5E8" 
                        CornerRadius="5" 
                        Padding="15">
                    <StackPanel>
                        <TextBlock Text="المشتريات والمشروبات" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                        
                        <DataGrid x:Name="DgPurchases" 
                                AutoGenerateColumns="False"
                                Height="200"
                                IsReadOnly="True">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="المنتج" Binding="{Binding ProductName}" Width="*"/>
                                <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                                <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice, StringFormat='{0:F2}'}" Width="80"/>
                                <DataGridTextColumn Header="الإجمالي" Binding="{Binding TotalPrice, StringFormat='{0:F2}'}" Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                        
                        <TextBlock x:Name="TxtPurchasesTotal" 
                                 Text="إجمالي المشتريات: 0.00 ريال" 
                                 FontWeight="Bold" 
                                 FontSize="16" 
                                 Foreground="#4CAF50"
                                 HorizontalAlignment="Left"
                                 Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- الإجمالي النهائي -->
        <Border Grid.Row="3" 
                Background="#2196F3" 
                CornerRadius="5" 
                Padding="20" 
                Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="إجمالي الوقت" Foreground="White" FontWeight="Bold"/>
                    <TextBlock x:Name="TxtFinalTimeTotal" Text="0.00 ريال" Foreground="White" FontSize="18"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1">
                    <TextBlock Text="إجمالي المشتريات" Foreground="White" FontWeight="Bold"/>
                    <TextBlock x:Name="TxtFinalPurchasesTotal" Text="0.00 ريال" Foreground="White" FontSize="18"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2">
                    <TextBlock Text="الإجمالي النهائي" Foreground="White" FontWeight="Bold"/>
                    <TextBlock x:Name="TxtGrandTotal" Text="0.00 ريال" Foreground="White" FontSize="24" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- الأزرار -->
        <StackPanel Grid.Row="4" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center">
            
            <Button x:Name="BtnPrint" 
                    Content="🖨️ طباعة الفاتورة" 
                    Background="#4CAF50" 
                    Foreground="White" 
                    Padding="20,10" 
                    Margin="0,0,10,0"
                    Click="BtnPrint_Click"/>
            
            <Button x:Name="BtnSave" 
                    Content="💾 حفظ الفاتورة" 
                    Background="#2196F3" 
                    Foreground="White" 
                    Padding="20,10" 
                    Margin="0,0,10,0"
                    Click="BtnSave_Click"/>
            
            <Button x:Name="BtnClose" 
                    Content="إغلاق" 
                    Background="#9E9E9E" 
                    Foreground="White" 
                    Padding="20,10"
                    Click="BtnClose_Click"/>
        </StackPanel>
    </Grid>
</Window>
