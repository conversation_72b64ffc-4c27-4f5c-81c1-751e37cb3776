<Page x:Class="WorkSpaceManager.Views.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="الرئيسية"
      FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- العنوان الرئيسي -->
            <TextBlock Grid.Row="0" 
                       Text="لوحة التحكم الرئيسية" 
                       FontSize="28" 
                       FontWeight="Bold" 
                       Margin="0,0,0,20"
                       Foreground="#2196F3"/>

            <!-- بطاقات الإحصائيات -->
            <Grid Grid.Row="1" Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- الجلسات النشطة -->
                <materialDesign:Card Grid.Column="0" 
                                   Margin="0,0,10,0" 
                                   Padding="20"
                                   Background="#4CAF50">
                    <StackPanel>
                        <TextBlock Text="⏰" FontSize="30" HorizontalAlignment="Center" Foreground="White"/>
                        <TextBlock x:Name="TxtActiveSessions" 
                                 Text="0" 
                                 FontSize="36" 
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center" 
                                 Foreground="White"/>
                        <TextBlock Text="جلسة نشطة" 
                                 FontSize="14" 
                                 HorizontalAlignment="Center" 
                                 Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- مبيعات اليوم -->
                <materialDesign:Card Grid.Column="1" 
                                   Margin="5,0" 
                                   Padding="20"
                                   Background="#2196F3">
                    <StackPanel>
                        <TextBlock Text="💰" FontSize="30" HorizontalAlignment="Center" Foreground="White"/>
                        <TextBlock x:Name="TxtTodaySales" 
                                 Text="0.00" 
                                 FontSize="36" 
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center" 
                                 Foreground="White"/>
                        <TextBlock Text="مبيعات اليوم" 
                                 FontSize="14" 
                                 HorizontalAlignment="Center" 
                                 Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- عدد العملاء -->
                <materialDesign:Card Grid.Column="2" 
                                   Margin="5,0" 
                                   Padding="20"
                                   Background="#FF9800">
                    <StackPanel>
                        <TextBlock Text="👥" FontSize="30" HorizontalAlignment="Center" Foreground="White"/>
                        <TextBlock x:Name="TxtTotalCustomers" 
                                 Text="0" 
                                 FontSize="36" 
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center" 
                                 Foreground="White"/>
                        <TextBlock Text="إجمالي العملاء" 
                                 FontSize="14" 
                                 HorizontalAlignment="Center" 
                                 Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- أرباح الشهر -->
                <materialDesign:Card Grid.Column="3" 
                                   Margin="10,0,0,0" 
                                   Padding="20"
                                   Background="#9C27B0">
                    <StackPanel>
                        <TextBlock Text="📈" FontSize="30" HorizontalAlignment="Center" Foreground="White"/>
                        <TextBlock x:Name="TxtMonthlyProfit" 
                                 Text="0.00" 
                                 FontSize="36" 
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center" 
                                 Foreground="White"/>
                        <TextBlock Text="أرباح الشهر" 
                                 FontSize="14" 
                                 HorizontalAlignment="Center" 
                                 Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- الإجراءات السريعة -->
            <Grid Grid.Row="2" Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- الإجراءات السريعة -->
                <materialDesign:Card Grid.Column="0" 
                                   Margin="0,0,10,0" 
                                   Padding="20">
                    <StackPanel>
                        <TextBlock Text="الإجراءات السريعة" 
                                 FontSize="18" 
                                 FontWeight="Bold" 
                                 Margin="0,0,0,15"
                                 Foreground="#2196F3"/>
                        
                        <Button x:Name="BtnStartSession" 
                                Content="🚀 بدء جلسة جديدة" 
                                Margin="0,5" 
                                Padding="15,10"
                                Background="#4CAF50"
                                Foreground="White"
                                Click="BtnStartSession_Click"/>
                        
                        <Button x:Name="BtnNewCustomer" 
                                Content="👤 إضافة عميل جديد" 
                                Margin="0,5" 
                                Padding="15,10"
                                Background="#2196F3"
                                Foreground="White"
                                Click="BtnNewCustomer_Click"/>
                        
                        <Button x:Name="BtnStartShift" 
                                Content="🔄 بدء شيفت جديد" 
                                Margin="0,5" 
                                Padding="15,10"
                                Background="#FF9800"
                                Foreground="White"
                                Click="BtnStartShift_Click"/>
                        
                        <Button x:Name="BtnCreateInvoice" 
                                Content="🧾 إنشاء فاتورة" 
                                Margin="0,5" 
                                Padding="15,10"
                                Background="#9C27B0"
                                Foreground="White"
                                Click="BtnCreateInvoice_Click"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- الجلسات النشطة -->
                <materialDesign:Card Grid.Column="1" 
                                   Margin="10,0,0,0" 
                                   Padding="20">
                    <StackPanel>
                        <TextBlock Text="الجلسات النشطة" 
                                 FontSize="18" 
                                 FontWeight="Bold" 
                                 Margin="0,0,0,15"
                                 Foreground="#2196F3"/>
                        
                        <ListView x:Name="LvActiveSessions" 
                                Height="200"
                                ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#F5F5F5" 
                                          CornerRadius="5" 
                                          Padding="10" 
                                          Margin="0,2">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="{Binding CustomerName}" 
                                                         FontWeight="Bold"/>
                                                <TextBlock Text="{Binding StartTime, StringFormat='بدأ في: {0:HH:mm}'}" 
                                                         FontSize="12" 
                                                         Foreground="Gray"/>
                                            </StackPanel>
                                            
                                            <Button Grid.Column="1" 
                                                  Content="إنهاء" 
                                                  Background="#F44336" 
                                                  Foreground="White" 
                                                  Padding="10,5"
                                                  Click="BtnEndSession_Click"
                                                  Tag="{Binding Id}"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- الأنشطة الأخيرة -->
            <materialDesign:Card Grid.Row="3" Padding="20">
                <StackPanel>
                    <TextBlock Text="الأنشطة الأخيرة" 
                             FontSize="18" 
                             FontWeight="Bold" 
                             Margin="0,0,0,15"
                             Foreground="#2196F3"/>
                    
                    <ListView x:Name="LvRecentActivities" 
                            Height="300"
                            ScrollViewer.VerticalScrollBarVisibility="Auto">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#F9F9F9" 
                                      CornerRadius="5" 
                                      Padding="15" 
                                      Margin="0,5">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" 
                                                 Text="{Binding Icon}" 
                                                 FontSize="20" 
                                                 Margin="0,0,15,0"/>
                                        
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding Description}" 
                                                     FontWeight="Medium"/>
                                            <TextBlock Text="{Binding Details}" 
                                                     FontSize="12" 
                                                     Foreground="Gray"/>
                                        </StackPanel>
                                        
                                        <TextBlock Grid.Column="2" 
                                                 Text="{Binding Time, StringFormat='{0:HH:mm}'}" 
                                                 FontSize="12" 
                                                 Foreground="Gray" 
                                                 VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </ScrollViewer>
</Page>
